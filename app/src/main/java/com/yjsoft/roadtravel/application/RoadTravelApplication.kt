package com.yjsoft.roadtravel.application

import android.app.Application
import android.content.pm.ApplicationInfo
import android.os.Build
import androidx.annotation.RequiresApi
import com.yjsoft.roadtravel.basiclibrary.datastore.core.DataStoreManager
import com.yjsoft.roadtravel.basiclibrary.di.HiltConfig
import com.yjsoft.roadtravel.basiclibrary.di.HiltDiagnostics
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogUtils
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import androidx.startup.AppInitializer
import com.yjsoft.roadtravel.basiclibrary.payment.core.PaymentManager
import com.yjsoft.roadtravel.startup.ImageManagerInitializer
import com.yjsoft.roadtravel.startup.PaymentManagerInitializer
import com.yjsoft.roadtravel.basiclibrary.datastore.model.CommonPreferenceKeys
import com.yjsoft.roadtravel.utils.BackgroundWarmupManager
import com.yjsoft.roadtravel.utils.PerformanceMonitor
import com.yjsoft.roadtravel.utils.StartupPerformanceReport
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject


/**
 * 应用程序类
 * 负责应用的全局初始化工作
 *
 * 使用Hilt进行依赖注入，同时保持向后兼容性
 */
@HiltAndroidApp
class RoadTravelApplication : Application() {

    // Hilt配置（可选注入，兼容模式）
    @Inject
    lateinit var hiltConfig: HiltConfig

    @RequiresApi(Build.VERSION_CODES.P)
    override fun onCreate() {
        // 开始应用启动性能监控
        PerformanceMonitor.startTiming("app_startup", "应用总启动时间")

        super.onCreate()

        // 开始Hilt初始化监控
        PerformanceMonitor.startTiming("hilt_init", "Hilt依赖注入初始化")

        // 初始化Hilt配置（如果可用）
        initializeHiltConfig()

        // 运行Hilt诊断（调试模式下）
        if (isDebugBuild()) {
            HiltDiagnostics.runDiagnostics(this)
        }

        PerformanceMonitor.endTiming("hilt_init", "Hilt依赖注入初始化完成")

        // 注意：核心组件（LogManager、DataStore、Network、Permission）
        // 现在通过androidx.startup自动初始化，无需在此处手动初始化
        PerformanceMonitor.recordStartupPhase("androidx.startup组件已自动初始化")

        // 延迟初始化非关键组件（按需加载）
        PerformanceMonitor.startTiming("delayed_components", "延迟组件初始化")
        initializeDelayedComponents()
        PerformanceMonitor.endTiming("delayed_components", "延迟组件初始化完成")

        // 记录应用启动日志
        LogManager.i("应用启动完成（使用androidx.startup优化）")
        LogManager.d("应用信息 - 包名: $packageName, 版本: ${getVersionInfo()}")

        // 启动后台预热
        BackgroundWarmupManager.startWarmup(
            context = this,
            delayMs = 1000L
        ) { taskName, current, total ->
            LogManager.d("BackgroundWarmup", "预热进度: $taskName ($current/$total)")
        }

        // 延迟执行日志统计（避免阻塞启动）
        performLogStatisticsAsync()

        // 结束应用启动监控并生成报告
        PerformanceMonitor.endTiming("app_startup", "应用启动完成")

        // 延迟输出性能报告（避免阻塞启动）
        Thread {
            Thread.sleep(1000) // 等待1秒确保所有组件初始化完成
            PerformanceMonitor.logReport()
            StartupPerformanceReport.generateComparisonReport()
        }.start()
    }

    /**
     * 初始化Hilt配置
     */
    private fun initializeHiltConfig() {
        try {
            LogManager.w("🔍 [RoadTravelApp] 开始检查Hilt依赖注入状态")

            // 检查Hilt是否可用（兼容模式）
            if (::hiltConfig.isInitialized) {
                LogManager.w("✅ [RoadTravelApp] Hilt依赖注入可用，开始初始化")
                hiltConfig.initialize()
                LogManager.w("🎉 [RoadTravelApp] Hilt依赖注入框架初始化成功")

                // 输出配置信息
                val configInfo = hiltConfig.getConfigInfo()
                LogManager.w("📋 [RoadTravelApp] Hilt配置信息: %s", configInfo)
            } else {
                LogManager.w("❌ [RoadTravelApp] Hilt未注入，可能的原因:")
                LogManager.w("1. @HiltAndroidApp注解未生效")
                LogManager.w("2. Hilt编译器未正确处理")
                LogManager.w("3. kapt配置问题")
                LogManager.w("4. 需要Clean Project并重新编译")
                LogManager.w("🔄 [RoadTravelApp] 使用手动初始化模式")
            }
        } catch (e: Exception) {
            // Hilt初始化失败，回退到手动初始化
            LogManager.e(e, "💥 [RoadTravelApp] Hilt初始化失败，回退到手动初始化")
        }
    }

    /**
     * 初始化延迟加载的组件
     * 这些组件不是应用启动必需的，可以在需要时再初始化
     */
    private fun initializeDelayedComponents() {
        try {
            LogManager.i("开始配置延迟加载组件")

            // 监控androidx.startup初始化状态
            monitorStartupInitialization()

            // 图片框架和支付框架将在实际使用时通过手动调用androidx.startup进行初始化
            // 这样可以显著减少应用启动时间

            LogManager.i("延迟加载组件配置完成")

        } catch (e: Exception) {
            LogManager.e(e, "延迟加载组件初始化失败")
        }
    }

    /**
     * 监控androidx.startup初始化状态
     */
    private fun monitorStartupInitialization() {
        try {
            val appInitializer = AppInitializer.getInstance(this)

            // 检查核心组件是否已通过startup初始化
            val isLogManagerInitialized = LogManager.isInitialized()
            val isDataStoreInitialized = checkDataStoreInitialized()
            val isNetworkInitialized = NetworkManager.isInitialized()

            LogManager.i("androidx.startup初始化状态检查:")
            LogManager.i("- LogManager: ${if (isLogManagerInitialized) "✅" else "❌"}")
            LogManager.i("- DataStore: ${if (isDataStoreInitialized) "✅" else "❌"}")
            LogManager.i("- Network: ${if (isNetworkInitialized) "✅" else "❌"}")

            // 如果有组件未初始化，记录警告
            if (!isLogManagerInitialized || !isDataStoreInitialized || !isNetworkInitialized) {
                LogManager.w("部分核心组件未通过androidx.startup正确初始化，可能影响应用功能")
            }

        } catch (e: Exception) {
            LogManager.e(e, "监控androidx.startup状态时发生错误")
        }
    }

    /**
     * 手动初始化图片框架（延迟加载）
     */
    fun initializeImageManagerIfNeeded() {
        try {
            if (!ImageManager.isInitialized()) {
                val appInitializer = AppInitializer.getInstance(this)
                appInitializer.initializeComponent(ImageManagerInitializer::class.java)
                LogManager.i("图片框架已通过手动startup初始化")
            }
        } catch (e: Exception) {
            LogManager.e(e, "手动初始化图片框架失败")
        }
    }

    /**
     * 手动初始化支付框架（延迟加载）
     */
    fun initializePaymentManagerIfNeeded() {
        try {
            if (!checkPaymentManagerInitialized()) {
                val appInitializer = AppInitializer.getInstance(this)
                appInitializer.initializeComponent(PaymentManagerInitializer::class.java)
                LogManager.i("支付框架已通过手动startup初始化")
            }
        } catch (e: Exception) {
            LogManager.e(e, "手动初始化支付框架失败")
        }
    }

    /**
     * 检查DataStore是否已初始化
     */
    private fun checkDataStoreInitialized(): Boolean {
        return try {
            // 尝试调用一个需要初始化的方法
            kotlinx.coroutines.runBlocking {
                DataStoreManager.getValue(CommonPreferenceKeys.APP_VERSION)
            }
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 检查PaymentManager是否已初始化
     */
    private fun checkPaymentManagerInitialized(): Boolean {
        return try {
            // 尝试访问paymentState属性，如果未初始化可能会有问题
            PaymentManager.paymentState.value
            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 异步执行日志统计（避免阻塞应用启动）
     */
    private fun performLogStatisticsAsync() {
        // 使用后台线程执行日志统计，避免阻塞主线程
        Thread {
            try {
                // 创建日志目录
                LogUtils.createLogDirectories(this)

                // 清理过期日志（保留7天）
                LogUtils.clearLogsOlderThan(this, 7)

                // 记录日志统计信息
                val stats = LogUtils.getLogStatistics(this)
                LogManager.d(
                    "日志统计 - 日志文件: ${stats.totalLogFiles}, 崩溃文件: ${stats.totalCrashFiles}, 总大小: ${
                        LogUtils.formatFileSize(
                            stats.totalLogSize
                        )
                    }"
                )

                LogManager.i("日志统计完成（后台执行）")

            } catch (e: Exception) {
                LogManager.e(e, "日志统计执行失败")
            }
        }.start()
    }













    /**
     * 判断是否为调试构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            val applicationInfo = applicationInfo
            (applicationInfo.flags and ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取版本信息
     */
    @RequiresApi(Build.VERSION_CODES.P)
    private fun getVersionInfo(): String {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            "${packageInfo.versionName} (${packageInfo.longVersionCode})"
        } catch (e: Exception) {
            "Unknown"
        }
    }

    override fun onTerminate() {
        super.onTerminate()

        // 记录应用终止日志
        LogManager.i("应用正在终止")

        // 清理网络框架资源
        NetworkManager.cleanup()

        // 清理图片框架资源
        ImageManager.clearAllCache()

        // 清理支付框架资源
        PaymentManager.cleanup()

        // 清理日志资源
        LogManager.cleanup()
    }

    override fun onLowMemory() {
        super.onLowMemory()
        LogManager.w("系统内存不足警告")
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)

        val levelName = when (level) {
            TRIM_MEMORY_RUNNING_MODERATE -> "RUNNING_MODERATE"
            TRIM_MEMORY_RUNNING_LOW -> "RUNNING_LOW"
            TRIM_MEMORY_RUNNING_CRITICAL -> "RUNNING_CRITICAL"
            TRIM_MEMORY_UI_HIDDEN -> "UI_HIDDEN"
            TRIM_MEMORY_BACKGROUND -> "BACKGROUND"
            TRIM_MEMORY_MODERATE -> "MODERATE"
            TRIM_MEMORY_COMPLETE -> "COMPLETE"
            else -> "UNKNOWN($level)"
        }

        LogManager.w("内存修剪请求: $levelName")

        // 根据内存压力级别清理图片缓存
        when (level) {
            TRIM_MEMORY_RUNNING_MODERATE,
            TRIM_MEMORY_RUNNING_LOW -> {
                // 中等内存压力：清理内存缓存
                ImageManager.clearMemoryCache()
                LogManager.d("已清理图片内存缓存（内存压力: $levelName）")
            }

            TRIM_MEMORY_RUNNING_CRITICAL,
            TRIM_MEMORY_COMPLETE -> {
                // 严重内存压力：清理所有缓存
                ImageManager.clearAllCache()
                LogManager.d("已清理所有图片缓存（内存压力: $levelName）")
            }
        }
    }
}