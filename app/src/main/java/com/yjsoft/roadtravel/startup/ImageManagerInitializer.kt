package com.yjsoft.roadtravel.startup

import android.content.Context
import androidx.startup.Initializer
import com.yjsoft.roadtravel.basiclibrary.image.ImageManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager

/**
 * 图片加载框架初始化器
 * 使用androidx.startup进行图片框架的初始化
 * 
 * 优先级：低（可延迟初始化，依赖LogManager）
 * 依赖：LogManager
 * 可选依赖：Network（用于网络图片加载优化）
 */
class ImageManagerInitializer : Initializer<ImageManager> {

    companion object {
        private const val TAG = "ImageManagerInitializer"
    }

    /**
     * 创建并初始化图片加载框架
     * @param context 应用上下文
     * @return ImageManager实例
     */
    override fun create(context: Context): ImageManager {
        try {
            // 根据构建类型选择配置
            val isDebug = isDebugBuild(context)

            // 初始化图片加载框架
            if (isDebug) {
                // 调试环境：启用详细日志和调试功能
                ImageManager.init(context)
                LogManager.d(TAG, "图片加载框架初始化成功（调试模式）")
            } else {
                // 发布环境：使用性能优化配置
                ImageManager.init(context)
                LogManager.d(TAG, "图片加载框架初始化成功（发布模式）")
            }

            // 记录图片框架状态
            val isInitialized = ImageManager.isInitialized()
            LogManager.d(TAG, "图片框架状态 - 已初始化: $isInitialized")
            
            LogManager.i(TAG, "图片加载框架通过androidx.startup初始化成功")
            
            return ImageManager
            
        } catch (e: Exception) {
            LogManager.e(TAG, "图片加载框架初始化失败", e)
            throw e
        }
    }

    /**
     * 返回此初始化器的依赖项
     * 图片框架依赖LogManager进行日志记录
     * @return 依赖LogManagerInitializer
     */
    override fun dependencies(): List<Class<out Initializer<*>>> {
        return listOf(LogManagerInitializer::class.java)
    }

    /**
     * 判断是否为调试构建
     */
    private fun isDebugBuild(context: Context): Boolean {
        return try {
            val applicationInfo = context.applicationInfo
            (applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
        } catch (e: Exception) {
            false
        }
    }
}
